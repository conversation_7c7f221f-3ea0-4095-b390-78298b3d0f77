import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Paper,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  AttachFile,
  Build,
  Assignment,
  RemoveRedEye,
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import {
  addServiceVisit,
  updateServiceVisit,
  deleteServiceVisit,
  addAttachment,
  removeAttachment,
} from '../store/slices/serviceVisitsSlice'

const ServiceVisitLogs = () => {
  const dispatch = useDispatch()
  const { serviceVisits } = useSelector(state => state.serviceVisits)
  const { devices } = useSelector(state => state.devices)
  const { user } = useSelector(state => state.auth)
  
  const [dialogOpen, setDialogOpen] = useState(false)
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false)
  const [editingVisit, setEditingVisit] = useState(null)
  const [selectedVisit, setSelectedVisit] = useState(null)
  const [formData, setFormData] = useState({
    deviceId: '',
    deviceType: '',
    facility: '',
    visitDate: '',
    engineer: '',
    purpose: 'Preventive',
    status: 'In Progress',
    notes: '',
    workPerformed: [],
    nextServiceDate: '',
    partsUsed: [],
  })

  const handleAddVisit = () => {
    setEditingVisit(null)
    setFormData({
      deviceId: '',
      deviceType: '',
      facility: '',
      visitDate: '',
      engineer: '',
      purpose: 'Preventive',
      status: 'In Progress',
      notes: '',
      workPerformed: [],
      nextServiceDate: '',
      partsUsed: [],
    })
    setDialogOpen(true)
  }

  const handleEditVisit = (visit) => {
    setEditingVisit(visit)
    setFormData(visit)
    setDialogOpen(true)
  }

  const handleSaveVisit = () => {
    const visitData = {
      ...formData,
      attachments: [],
    }

    if (editingVisit) {
      dispatch(updateServiceVisit({ ...visitData, id: editingVisit.id }))
    } else {
      dispatch(addServiceVisit(visitData))
    }
    setDialogOpen(false)
  }

  const handleDeleteVisit = (visitId) => {
    if (window.confirm('Are you sure you want to delete this service visit?')) {
      dispatch(deleteServiceVisit(visitId))
    }
  }

  const handleFileUpload = (visitId, event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        dispatch(addAttachment({
          visitId,
          attachment: {
            name: file.name,
            url: e.target.result,
            uploadDate: new Date().toISOString(),
            type: file.type,
          }
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success'
      case 'In Progress': return 'warning'
      case 'Scheduled': return 'info'
      default: return 'default'
    }
  }

  const getPurposeColor = (purpose) => {
    switch (purpose) {
      case 'Preventive': return 'primary'
      case 'Breakdown': return 'error'
      case 'Calibration': return 'secondary'
      default: return 'default'
    }
  }

  const columns = [
    { field: 'id', headerName: 'Visit ID', width: 120 },
    { field: 'deviceId', headerName: 'Device ID', width: 120 },
    { field: 'deviceType', headerName: 'Device Type', width: 150 },
    { field: 'facility', headerName: 'Facility', width: 150 },
    { field: 'visitDate', headerName: 'Visit Date', width: 120 },
    { field: 'engineer', headerName: 'Engineer', width: 130 },
    {
      field: 'purpose',
      headerName: 'Purpose',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getPurposeColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 180,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedVisit(params.row)
              setDetailsDialogOpen(true)
            }}
          >
            <RemoveRedEye />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleEditVisit(params.row)}
            disabled={!user?.permissions.includes('write')}
          >
            <Edit />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDeleteVisit(params.row.id)}
            disabled={!user?.permissions.includes('delete')}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ]

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Service Visit Logs
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleAddVisit}
          disabled={!user?.permissions.includes('write')}
        >
          New Service Visit
        </Button>
      </Box>

      <Paper>
        <DataGrid
          rows={serviceVisits}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          autoHeight
          disableRowSelectionOnClick
        />
      </Paper>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingVisit ? 'Edit Service Visit' : 'New Service Visit'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={formData.deviceId}
                  onChange={(e) => {
                    const device = devices.find(d => d.id === e.target.value)
                    setFormData({
                      ...formData,
                      deviceId: e.target.value,
                      deviceType: device?.type || '',
                      facility: device?.facility || '',
                    })
                  }}
                >
                  {devices.map((device) => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.id} - {device.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Device Type"
                value={formData.deviceType}
                onChange={(e) => setFormData({ ...formData, deviceType: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Facility"
                value={formData.facility}
                onChange={(e) => setFormData({ ...formData, facility: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Visit Date"
                type="date"
                value={formData.visitDate}
                onChange={(e) => setFormData({ ...formData, visitDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Engineer"
                value={formData.engineer}
                onChange={(e) => setFormData({ ...formData, engineer: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Purpose</InputLabel>
                <Select
                  value={formData.purpose}
                  onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
                >
                  <MenuItem value="Preventive">Preventive</MenuItem>
                  <MenuItem value="Breakdown">Breakdown</MenuItem>
                  <MenuItem value="Calibration">Calibration</MenuItem>
                  <MenuItem value="Installation">Installation</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value="Scheduled">Scheduled</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Next Service Date"
                type="date"
                value={formData.nextServiceDate}
                onChange={(e) => setFormData({ ...formData, nextServiceDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Work Performed (comma separated)"
                value={formData.workPerformed.join(', ')}
                onChange={(e) => setFormData({
                  ...formData,
                  workPerformed: e.target.value.split(',').map(item => item.trim()).filter(Boolean)
                })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveVisit} variant="contained">
            {editingVisit ? 'Update' : 'Create'} Visit
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Service Visit Details - {selectedVisit?.id}</DialogTitle>
        <DialogContent>
          {selectedVisit && (
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Visit Information
                      </Typography>
                      <Typography><strong>Device:</strong> {selectedVisit.deviceType} ({selectedVisit.deviceId})</Typography>
                      <Typography><strong>Facility:</strong> {selectedVisit.facility}</Typography>
                      <Typography><strong>Date:</strong> {selectedVisit.visitDate}</Typography>
                      <Typography><strong>Engineer:</strong> {selectedVisit.engineer}</Typography>
                      <Typography><strong>Purpose:</strong>
                        <Chip
                          label={selectedVisit.purpose}
                          color={getPurposeColor(selectedVisit.purpose)}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                      <Typography><strong>Status:</strong>
                        <Chip
                          label={selectedVisit.status}
                          color={getStatusColor(selectedVisit.status)}
                          size="small"
                          sx={{ ml: 1 }}
                        />
                      </Typography>
                      <Typography><strong>Next Service:</strong> {selectedVisit.nextServiceDate}</Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Work Performed
                      </Typography>
                      <List dense>
                        {selectedVisit.workPerformed.map((work, index) => (
                          <ListItem key={index}>
                            <ListItemText primary={work} />
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Parts Used
                      </Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>Part Name</TableCell>
                              <TableCell>Part Number</TableCell>
                              <TableCell>Quantity</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {selectedVisit.partsUsed.map((part, index) => (
                              <TableRow key={index}>
                                <TableCell>{part.name}</TableCell>
                                <TableCell>{part.partNumber}</TableCell>
                                <TableCell>{part.quantity}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Notes
                      </Typography>
                      <Typography>{selectedVisit.notes}</Typography>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="h6">
                          Attachments
                        </Typography>
                        <input
                          accept="*/*"
                          style={{ display: 'none' }}
                          id="attachment-upload"
                          type="file"
                          onChange={(e) => handleFileUpload(selectedVisit.id, e)}
                        />
                        <label htmlFor="attachment-upload">
                          <Button
                            variant="outlined"
                            component="span"
                            startIcon={<AttachFile />}
                            disabled={!user?.permissions.includes('write')}
                          >
                            Add Attachment
                          </Button>
                        </label>
                      </Box>

                      <List>
                        {selectedVisit.attachments.map((attachment, index) => (
                          <ListItem key={index}>
                            <ListItemText
                              primary={attachment.name}
                              secondary={`Uploaded: ${new Date(attachment.uploadDate).toLocaleDateString()}`}
                            />
                            <ListItemSecondaryAction>
                              <IconButton
                                edge="end"
                                onClick={() => dispatch(removeAttachment({ visitId: selectedVisit.id, attachmentIndex: index }))}
                                disabled={!user?.permissions.includes('write')}
                              >
                                <Delete />
                              </IconButton>
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default ServiceVisitLogs
