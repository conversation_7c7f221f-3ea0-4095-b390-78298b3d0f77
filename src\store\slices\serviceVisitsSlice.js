import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  serviceVisits: [
    {
      id: 'SV001',
      deviceId: 'DEV001',
      deviceType: 'Ventilator',
      facility: 'City Hospital',
      visitDate: '2024-06-15',
      engineer: '<PERSON>',
      purpose: 'Preventive',
      status: 'Completed',
      notes: 'Routine maintenance completed. All systems functioning normally.',
      attachments: [],
      workPerformed: [
        'Filter replacement',
        'Calibration check',
        'Software update',
        'Performance testing',
      ],
      nextServiceDate: '2024-09-15',
      partsUsed: [
        { name: 'Air Filter', quantity: 2, partNumber: 'AF-001' },
        { name: 'O-Ring Seal', quantity: 1, partNumber: 'OR-205' },
      ],
    },
    {
      id: 'SV002',
      deviceId: 'DEV002',
      deviceType: 'Patient Monitor',
      facility: 'General Hospital',
      visitDate: '2024-06-10',
      engineer: '<PERSON>',
      purpose: 'Breakdown',
      status: 'Completed',
      notes: 'Display issue resolved. Replaced faulty LCD panel.',
      attachments: [],
      workPerformed: [
        'Diagnostic testing',
        'LCD panel replacement',
        'System calibration',
        'User training',
      ],
      nextServiceDate: '2024-12-10',
      partsUsed: [
        { name: 'LCD Panel', quantity: 1, partNumber: 'LCD-15-HD' },
      ],
    },
    {
      id: 'SV003',
      deviceId: 'DEV003',
      deviceType: 'X-Ray Machine',
      facility: 'City Hospital',
      visitDate: '2024-06-20',
      engineer: 'Mike Wilson',
      purpose: 'Preventive',
      status: 'In Progress',
      notes: 'Annual maintenance in progress. Tube replacement required.',
      attachments: [],
      workPerformed: [
        'Safety inspection',
        'Radiation testing',
      ],
      nextServiceDate: '2025-06-20',
      partsUsed: [],
    },
  ],
  loading: false,
  error: null,
}

const serviceVisitsSlice = createSlice({
  name: 'serviceVisits',
  initialState,
  reducers: {
    addServiceVisit: (state, action) => {
      state.serviceVisits.push({
        ...action.payload,
        id: `SV${String(state.serviceVisits.length + 1).padStart(3, '0')}`,
      })
    },
    updateServiceVisit: (state, action) => {
      const index = state.serviceVisits.findIndex(visit => visit.id === action.payload.id)
      if (index !== -1) {
        state.serviceVisits[index] = { ...state.serviceVisits[index], ...action.payload }
      }
    },
    deleteServiceVisit: (state, action) => {
      state.serviceVisits = state.serviceVisits.filter(visit => visit.id !== action.payload)
    },
    addAttachment: (state, action) => {
      const { visitId, attachment } = action.payload
      const visit = state.serviceVisits.find(visit => visit.id === visitId)
      if (visit) {
        visit.attachments.push(attachment)
      }
    },
    removeAttachment: (state, action) => {
      const { visitId, attachmentIndex } = action.payload
      const visit = state.serviceVisits.find(visit => visit.id === visitId)
      if (visit) {
        visit.attachments.splice(attachmentIndex, 1)
      }
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
  },
})

export const {
  addServiceVisit,
  updateServiceVisit,
  deleteServiceVisit,
  addAttachment,
  removeAttachment,
  setLoading,
  setError,
} = serviceVisitsSlice.actions

export default serviceVisitsSlice.reducer
