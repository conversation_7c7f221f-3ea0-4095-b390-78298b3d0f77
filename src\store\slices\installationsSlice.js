import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  installations: [
    {
      id: 'INST001',
      deviceId: 'DEV001',
      deviceType: 'Ventilator',
      facility: 'City Hospital',
      installationDate: '2024-01-15',
      technician: '<PERSON>',
      status: 'Completed',
      unboxingPhotos: [],
      checklist: {
        deviceInspection: true,
        functionalTesting: true,
        calibration: true,
        userTraining: true,
        documentation: true,
      },
      trainingForm: {
        trainedPersonnel: ['Dr. <PERSON>', 'Nurse <PERSON>'],
        trainingDate: '2024-01-16',
        trainingDuration: '2 hours',
        competencyAssessment: 'Passed',
      },
      notes: 'Installation completed successfully. All staff trained.',
    },
    {
      id: 'INST002',
      deviceId: 'DEV002',
      deviceType: 'Patient Monitor',
      facility: 'General Hospital',
      installationDate: '2024-02-20',
      technician: '<PERSON>',
      status: 'In Progress',
      unboxingPhotos: [],
      checklist: {
        deviceInspection: true,
        functionalTesting: true,
        calibration: false,
        userTraining: false,
        documentation: false,
      },
      trainingForm: {
        trainedPersonnel: [],
        trainingDate: null,
        trainingDuration: null,
        competencyAssessment: null,
      },
      notes: 'Installation in progress. Calibration pending.',
    },
  ],
  loading: false,
  error: null,
}

const installationsSlice = createSlice({
  name: 'installations',
  initialState,
  reducers: {
    addInstallation: (state, action) => {
      state.installations.push({
        ...action.payload,
        id: `INST${String(state.installations.length + 1).padStart(3, '0')}`,
      })
    },
    updateInstallation: (state, action) => {
      const index = state.installations.findIndex(inst => inst.id === action.payload.id)
      if (index !== -1) {
        state.installations[index] = { ...state.installations[index], ...action.payload }
      }
    },
    deleteInstallation: (state, action) => {
      state.installations = state.installations.filter(inst => inst.id !== action.payload)
    },
    updateChecklist: (state, action) => {
      const { installationId, checklist } = action.payload
      const installation = state.installations.find(inst => inst.id === installationId)
      if (installation) {
        installation.checklist = { ...installation.checklist, ...checklist }
      }
    },
    updateTrainingForm: (state, action) => {
      const { installationId, trainingForm } = action.payload
      const installation = state.installations.find(inst => inst.id === installationId)
      if (installation) {
        installation.trainingForm = { ...installation.trainingForm, ...trainingForm }
      }
    },
    addUnboxingPhoto: (state, action) => {
      const { installationId, photo } = action.payload
      const installation = state.installations.find(inst => inst.id === installationId)
      if (installation) {
        installation.unboxingPhotos.push(photo)
      }
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
  },
})

export const {
  addInstallation,
  updateInstallation,
  deleteInstallation,
  updateChecklist,
  updateTrainingForm,
  addUnboxingPhoto,
  setLoading,
  setError,
} = installationsSlice.actions

export default installationsSlice.reducer
