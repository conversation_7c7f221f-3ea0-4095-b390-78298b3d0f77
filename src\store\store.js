import { configureStore } from '@reduxjs/toolkit'
import devicesReducer from './slices/devicesSlice'
import installationsReducer from './slices/installationsSlice'
import serviceVisitsReducer from './slices/serviceVisitsSlice'
import amcCmcReducer from './slices/amcCmcSlice'
import alertsReducer from './slices/alertsSlice'
import authReducer from './slices/authSlice'
import themeReducer from './slices/themeSlice'

export const store = configureStore({
  reducer: {
    devices: devicesReducer,
    installations: installationsReducer,
    serviceVisits: serviceVisitsReducer,
    amcCmc: amcCmcReducer,
    alerts: alertsReducer,
    auth: authReducer,
    theme: themeReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

const saveToLocalStorage = (state) => {
  try {
    const serializedState = JSON.stringify(state)
    localStorage.setItem('deviceCrmState', serializedState)
  } catch (error) {
    console.warn('Could not save state to localStorage:', error)
  }
}

const loadFromLocalStorage = () => {
  try {
    const serializedState = localStorage.getItem('deviceCrmState')
    if (serializedState === null) return undefined
    return JSON.parse(serializedState)
  } catch (error) {
    console.warn('Could not load state from localStorage:', error)
    return undefined
  }
}

store.subscribe(() => {
  saveToLocalStorage(store.getState())
})

const persistedState = loadFromLocalStorage()
if (persistedState) {
  store.dispatch({ type: 'HYDRATE', payload: persistedState })
}
