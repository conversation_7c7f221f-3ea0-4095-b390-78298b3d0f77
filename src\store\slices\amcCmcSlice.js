import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  contracts: [
    {
      id: 'AMC001',
      deviceId: 'DEV001',
      deviceType: 'Ventilator',
      facility: 'City Hospital',
      contractType: 'AMC',
      startDate: '2024-01-15',
      endDate: '2025-01-14',
      status: 'Active',
      vendor: 'MedTech Inc',
      contractValue: 25000,
      currency: 'USD',
      serviceFrequency: 'Quarterly',
      lastServiceDate: '2024-06-15',
      nextServiceDate: '2024-09-15',
      contactPerson: '<PERSON>',
      contactEmail: '<EMAIL>',
      contactPhone: '******-0123',
      terms: 'Comprehensive maintenance including parts and labor',
      renewalNotificationDays: 30,
    },
    {
      id: 'CMC001',
      deviceId: 'DEV002',
      deviceType: 'Patient Monitor',
      facility: 'General Hospital',
      contractType: 'CMC',
      startDate: '2024-02-20',
      endDate: '2024-08-19',
      status: 'Expired',
      vendor: 'HealthTech Ltd',
      contractValue: 15000,
      currency: 'USD',
      serviceFrequency: 'Monthly',
      lastServiceDate: '2024-06-10',
      nextServiceDate: '2024-07-10',
      contactPerson: '<PERSON>',
      contactEmail: '<EMAIL>',
      contactPhone: '******-0456',
      terms: 'Calibration and maintenance services only',
      renewalNotificationDays: 30,
    },
    {
      id: 'AMC002',
      deviceId: 'DEV003',
      deviceType: 'X-Ray Machine',
      facility: 'City Hospital',
      contractType: 'AMC',
      startDate: '2024-03-10',
      endDate: '2025-03-09',
      status: 'Active',
      vendor: 'ImageCorp',
      contractValue: 45000,
      currency: 'USD',
      serviceFrequency: 'Bi-annual',
      lastServiceDate: '2024-06-20',
      nextServiceDate: '2024-12-20',
      contactPerson: 'Mike Davis',
      contactEmail: '<EMAIL>',
      contactPhone: '******-0789',
      terms: 'Full service including radiation safety checks',
      renewalNotificationDays: 60,
    },
  ],
  loading: false,
  error: null,
}

const amcCmcSlice = createSlice({
  name: 'amcCmc',
  initialState,
  reducers: {
    addContract: (state, action) => {
      const contractType = action.payload.contractType
      const existingCount = state.contracts.filter(c => c.contractType === contractType).length
      state.contracts.push({
        ...action.payload,
        id: `${contractType}${String(existingCount + 1).padStart(3, '0')}`,
      })
    },
    updateContract: (state, action) => {
      const index = state.contracts.findIndex(contract => contract.id === action.payload.id)
      if (index !== -1) {
        state.contracts[index] = { ...state.contracts[index], ...action.payload }
      }
    },
    deleteContract: (state, action) => {
      state.contracts = state.contracts.filter(contract => contract.id !== action.payload)
    },
    renewContract: (state, action) => {
      const { contractId, newEndDate } = action.payload
      const contract = state.contracts.find(c => c.id === contractId)
      if (contract) {
        contract.endDate = newEndDate
        contract.status = 'Active'
      }
    },
    markExpired: (state, action) => {
      const contract = state.contracts.find(c => c.id === action.payload)
      if (contract) {
        contract.status = 'Expired'
      }
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
  },
})

export const {
  addContract,
  updateContract,
  deleteContract,
  renewContract,
  markExpired,
  setLoading,
  setError,
} = amcCmcSlice.actions

export default amcCmcSlice.reducer
