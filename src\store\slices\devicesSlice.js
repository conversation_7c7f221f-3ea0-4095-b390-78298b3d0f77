import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  devices: [
    {
      id: 'DEV001',
      type: 'Ventilator',
      facility: 'City Hospital',
      status: 'Online',
      batteryLevel: 85,
      lastServiceDate: '2024-06-15',
      amcCmcStatus: 'Active',
      location: 'ICU-1',
      serialNumber: 'VNT-2024-001',
      manufacturer: 'MedTech Inc',
      model: 'VT-Pro-500',
      installationDate: '2024-01-15',
    },
    {
      id: 'DEV002',
      type: 'Patient Monitor',
      facility: 'General Hospital',
      status: 'Offline',
      batteryLevel: 45,
      lastServiceDate: '2024-06-10',
      amcCmcStatus: 'Expired',
      location: 'Ward-A',
      serialNumber: 'PM-2024-002',
      manufacturer: 'HealthTech Ltd',
      model: 'PM-Advanced-200',
      installationDate: '2024-02-20',
    },
    {
      id: 'DEV003',
      type: 'X-Ray Machine',
      facility: 'City Hospital',
      status: 'Maintenance',
      batteryLevel: 0,
      lastServiceDate: '2024-06-20',
      amcCmcStatus: 'Active',
      location: 'Radiology',
      serialNumber: 'XR-2024-003',
      manufacturer: 'ImageCorp',
      model: 'XR-Digital-Pro',
      installationDate: '2024-03-10',
    },
  ],
  loading: false,
  error: null,
}

const devicesSlice = createSlice({
  name: 'devices',
  initialState,
  reducers: {
    addDevice: (state, action) => {
      state.devices.push({
        ...action.payload,
        id: `DEV${String(state.devices.length + 1).padStart(3, '0')}`,
      })
    },
    updateDevice: (state, action) => {
      const index = state.devices.findIndex(device => device.id === action.payload.id)
      if (index !== -1) {
        state.devices[index] = { ...state.devices[index], ...action.payload }
      }
    },
    deleteDevice: (state, action) => {
      state.devices = state.devices.filter(device => device.id !== action.payload)
    },
    setDevices: (state, action) => {
      state.devices = action.payload
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
  },
})

export const {
  addDevice,
  updateDevice,
  deleteDevice,
  setDevices,
  setLoading,
  setError,
} = devicesSlice.actions

export default devicesSlice.reducer
