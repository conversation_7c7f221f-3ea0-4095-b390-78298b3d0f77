import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  LinearProgress,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  ViewModule,
  ViewList,
  Battery20,
  Battery50,
  Battery80,
  BatteryFull,
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { addDevice, updateDevice, deleteDevice } from '../store/slices/devicesSlice'

const DeviceInventoryDashboard = () => {
  const dispatch = useDispatch()
  const { devices } = useSelector(state => state.devices)
  const { user } = useSelector(state => state.auth)
  
  const [viewMode, setViewMode] = useState('cards')
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingDevice, setEditingDevice] = useState(null)
  const [formData, setFormData] = useState({
    type: '',
    facility: '',
    status: 'Online',
    batteryLevel: 100,
    location: '',
    serialNumber: '',
    manufacturer: '',
    model: '',
    installationDate: '',
  })

  const handleAddDevice = () => {
    setEditingDevice(null)
    setFormData({
      type: '',
      facility: '',
      status: 'Online',
      batteryLevel: 100,
      location: '',
      serialNumber: '',
      manufacturer: '',
      model: '',
      installationDate: '',
    })
    setDialogOpen(true)
  }

  const handleEditDevice = (device) => {
    setEditingDevice(device)
    setFormData(device)
    setDialogOpen(true)
  }

  const handleSaveDevice = () => {
    if (editingDevice) {
      dispatch(updateDevice({ ...formData, id: editingDevice.id }))
    } else {
      dispatch(addDevice({
        ...formData,
        lastServiceDate: new Date().toISOString().split('T')[0],
        amcCmcStatus: 'Active',
      }))
    }
    setDialogOpen(false)
  }

  const handleDeleteDevice = (deviceId) => {
    if (window.confirm('Are you sure you want to delete this device?')) {
      dispatch(deleteDevice(deviceId))
    }
  }

  const getBatteryIcon = (level) => {
    if (level <= 20) return <Battery20 color="error" />
    if (level <= 50) return <Battery50 color="warning" />
    if (level <= 80) return <Battery80 color="primary" />
    return <BatteryFull color="success" />
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Online': return 'success'
      case 'Offline': return 'error'
      case 'Maintenance': return 'warning'
      default: return 'default'
    }
  }

  const columns = [
    { field: 'id', headerName: 'Device ID', width: 120 },
    { field: 'type', headerName: 'Type', width: 150 },
    { field: 'facility', headerName: 'Facility', width: 150 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'batteryLevel',
      headerName: 'Battery %',
      width: 120,
      renderCell: (params) => (
        <Box display="flex" alignItems="center" gap={1}>
          {getBatteryIcon(params.value)}
          <Typography variant="body2">{params.value}%</Typography>
        </Box>
      ),
    },
    { field: 'lastServiceDate', headerName: 'Last Service', width: 130 },
    {
      field: 'amcCmcStatus',
      headerName: 'AMC/CMC',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={params.value === 'Active' ? 'success' : 'error'}
          size="small"
        />
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 150,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEditDevice(params.row)}
            disabled={!user?.permissions.includes('write')}
          >
            <Edit />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDeleteDevice(params.row.id)}
            disabled={!user?.permissions.includes('delete')}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ]

  const renderCards = () => (
    <Grid container spacing={3}>
      {devices.map((device) => (
        <Grid item xs={12} sm={6} md={4} key={device.id}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                <Typography variant="h6" component="div">
                  {device.type}
                </Typography>
                <Chip
                  label={device.status}
                  color={getStatusColor(device.status)}
                  size="small"
                />
              </Box>
              
              <Typography color="text.secondary" gutterBottom>
                ID: {device.id}
              </Typography>
              
              <Typography variant="body2" mb={1}>
                Facility: {device.facility}
              </Typography>
              
              <Typography variant="body2" mb={1}>
                Location: {device.location}
              </Typography>
              
              <Box display="flex" alignItems="center" gap={1} mb={1}>
                {getBatteryIcon(device.batteryLevel)}
                <Typography variant="body2">
                  Battery: {device.batteryLevel}%
                </Typography>
              </Box>
              
              <Typography variant="body2" mb={1}>
                Last Service: {device.lastServiceDate}
              </Typography>
              
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                <Chip
                  label={`AMC/CMC: ${device.amcCmcStatus}`}
                  color={device.amcCmcStatus === 'Active' ? 'success' : 'error'}
                  size="small"
                />
                <Box>
                  <IconButton
                    size="small"
                    onClick={() => handleEditDevice(device)}
                    disabled={!user?.permissions.includes('write')}
                  >
                    <Edit />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => handleDeleteDevice(device.id)}
                    disabled={!user?.permissions.includes('delete')}
                  >
                    <Delete />
                  </IconButton>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  )

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Device Inventory Dashboard
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          <ToggleButtonGroup
            value={viewMode}
            exclusive
            onChange={(e, newMode) => newMode && setViewMode(newMode)}
            size="small"
          >
            <ToggleButton value="cards">
              <ViewModule />
            </ToggleButton>
            <ToggleButton value="table">
              <ViewList />
            </ToggleButton>
          </ToggleButtonGroup>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddDevice}
            disabled={!user?.permissions.includes('write')}
          >
            Add Device
          </Button>
        </Box>
      </Box>

      {viewMode === 'cards' ? renderCards() : (
        <Paper>
          <DataGrid
            rows={devices}
            columns={columns}
            initialState={{
              pagination: {
                paginationModel: { pageSize: 10 },
              },
            }}
            pageSizeOptions={[10, 25, 50]}
            autoHeight
            disableRowSelectionOnClick
          />
        </Paper>
      )}

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingDevice ? 'Edit Device' : 'Add New Device'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Device Type"
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Facility"
                value={formData.facility}
                onChange={(e) => setFormData({ ...formData, facility: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value="Online">Online</MenuItem>
                  <MenuItem value="Offline">Offline</MenuItem>
                  <MenuItem value="Maintenance">Maintenance</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Battery Level (%)"
                type="number"
                value={formData.batteryLevel}
                onChange={(e) => setFormData({ ...formData, batteryLevel: parseInt(e.target.value) })}
                inputProps={{ min: 0, max: 100 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Serial Number"
                value={formData.serialNumber}
                onChange={(e) => setFormData({ ...formData, serialNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Manufacturer"
                value={formData.manufacturer}
                onChange={(e) => setFormData({ ...formData, manufacturer: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Model"
                value={formData.model}
                onChange={(e) => setFormData({ ...formData, model: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Installation Date"
                type="date"
                value={formData.installationDate}
                onChange={(e) => setFormData({ ...formData, installationDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveDevice} variant="contained">
            {editingDevice ? 'Update' : 'Add'} Device
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default DeviceInventoryDashboard
