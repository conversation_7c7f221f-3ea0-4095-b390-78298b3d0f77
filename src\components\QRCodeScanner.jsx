import React, { useState, useRef } from 'react'
import { useSelector } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Paper,
  Alert,
  AlertTitle,
  Grid,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material'
import {
  QrCodeScanner,
  CameraAlt,
  DevicesOther,
  LocationOn,
  Battery80,
  Build,
  CheckCircle,
  Error,
} from '@mui/icons-material'

const QRCodeScanner = () => {
  const { devices } = useSelector(state => state.devices)
  const [isScanning, setIsScanning] = useState(false)
  const [scannedData, setScannedData] = useState(null)
  const [deviceInfo, setDeviceInfo] = useState(null)
  const [error, setError] = useState(null)
  const videoRef = useRef(null)
  const canvasRef = useRef(null)

  const startScanning = async () => {
    try {
      setError(null)
      setIsScanning(true)
      
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
      }
    } catch (err) {
      setError('Camera access denied or not available')
      setIsScanning(false)
    }
  }

  const stopScanning = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = videoRef.current.srcObject.getTracks()
      tracks.forEach(track => track.stop())
      videoRef.current.srcObject = null
    }
    setIsScanning(false)
  }

  const simulateQRScan = (deviceId) => {
    const device = devices.find(d => d.id === deviceId)
    if (device) {
      setScannedData(deviceId)
      setDeviceInfo(device)
      setError(null)
    } else {
      setError('Device not found in inventory')
      setScannedData(deviceId)
      setDeviceInfo(null)
    }
    stopScanning()
  }

  const generateQRCode = (deviceId) => {
    const qrData = `DEVICE:${deviceId}`
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData)}`
    return qrCodeUrl
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Online': return 'success'
      case 'Offline': return 'error'
      case 'Maintenance': return 'warning'
      default: return 'default'
    }
  }

  const getBatteryIcon = (level) => {
    return <Battery80 color={level > 20 ? 'primary' : 'error'} />
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        QR Code Scanner
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Scan Device QR Code
              </Typography>
              
              {!isScanning ? (
                <Box textAlign="center" py={4}>
                  <QrCodeScanner sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="body1" color="text.secondary" gutterBottom>
                    Click the button below to start scanning
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<CameraAlt />}
                    onClick={startScanning}
                    size="large"
                  >
                    Start Scanner
                  </Button>
                </Box>
              ) : (
                <Box>
                  <video
                    ref={videoRef}
                    style={{
                      width: '100%',
                      maxWidth: 400,
                      height: 300,
                      objectFit: 'cover',
                      borderRadius: 8,
                    }}
                  />
                  <canvas ref={canvasRef} style={{ display: 'none' }} />
                  <Box mt={2} textAlign="center">
                    <Button
                      variant="outlined"
                      onClick={stopScanning}
                      sx={{ mr: 1 }}
                    >
                      Stop Scanner
                    </Button>
                  </Box>
                </Box>
              )}

              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <AlertTitle>Scanner Error</AlertTitle>
                  {error}
                </Alert>
              )}

              <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
                Demo: Simulate QR Scan
              </Typography>
              <Box display="flex" gap={1} flexWrap="wrap">
                {devices.slice(0, 3).map((device) => (
                  <Button
                    key={device.id}
                    variant="outlined"
                    size="small"
                    onClick={() => simulateQRScan(device.id)}
                  >
                    Scan {device.id}
                  </Button>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          {scannedData && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Scan Result
                </Typography>
                
                <Alert severity={deviceInfo ? 'success' : 'error'} sx={{ mb: 2 }}>
                  <AlertTitle>
                    {deviceInfo ? 'Device Found' : 'Device Not Found'}
                  </AlertTitle>
                  Scanned QR Code: {scannedData}
                </Alert>

                {deviceInfo && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Device Information
                    </Typography>
                    
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <DevicesOther />
                        </ListItemIcon>
                        <ListItemText
                          primary="Device Type"
                          secondary={deviceInfo.type}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <LocationOn />
                        </ListItemIcon>
                        <ListItemText
                          primary="Location"
                          secondary={`${deviceInfo.facility} - ${deviceInfo.location}`}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          {getBatteryIcon(deviceInfo.batteryLevel)}
                        </ListItemIcon>
                        <ListItemText
                          primary="Battery Level"
                          secondary={`${deviceInfo.batteryLevel}%`}
                        />
                      </ListItem>
                      
                      <ListItem>
                        <ListItemIcon>
                          <Build />
                        </ListItemIcon>
                        <ListItemText
                          primary="Last Service"
                          secondary={deviceInfo.lastServiceDate}
                        />
                      </ListItem>
                    </List>

                    <Box mt={2}>
                      <Typography variant="body2" gutterBottom>
                        Status:
                      </Typography>
                      <Chip
                        label={deviceInfo.status}
                        color={getStatusColor(deviceInfo.status)}
                        icon={deviceInfo.status === 'Online' ? <CheckCircle /> : <Error />}
                      />
                    </Box>

                    <Box mt={2}>
                      <Typography variant="body2" gutterBottom>
                        AMC/CMC Status:
                      </Typography>
                      <Chip
                        label={deviceInfo.amcCmcStatus}
                        color={deviceInfo.amcCmcStatus === 'Active' ? 'success' : 'error'}
                      />
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          )}
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Device QR Codes
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                QR codes for all devices in the inventory
              </Typography>
              
              <Grid container spacing={2} sx={{ mt: 1 }}>
                {devices.map((device) => (
                  <Grid item xs={6} sm={4} md={3} lg={2} key={device.id}>
                    <Paper sx={{ p: 2, textAlign: 'center' }}>
                      <img
                        src={generateQRCode(device.id)}
                        alt={`QR Code for ${device.id}`}
                        style={{ width: '100%', maxWidth: 120 }}
                      />
                      <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                        {device.id}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {device.type}
                      </Typography>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  )
}

export default QRCodeScanner
