@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #60a5fa;
  --background-primary: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #64748b;
  --border-color: #334155;
  --border-light: #475569;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-secondary);
}

.main-content {
  flex: 1;
  padding: 2rem;
  background: var(--background-secondary);
  transition: all var(--transition-normal);

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.card-container {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);

    &::before {
      opacity: 1;
    }
  }
}

.form-container {
  max-width: 700px;
  margin: 0 auto;
  background: var(--background-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--success-color));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  }

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1.5rem;
  }
}

.status-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.online {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
  }

  &.offline {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  &.maintenance {
    background: linear-gradient(135deg, #fefbeb, #fed7aa);
    color: #92400e;
    border: 1px solid #fdba74;
  }

  &.active {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border: 1px solid #93c5fd;
  }

  &.expired {
    background: linear-gradient(135deg, #fdf2f8, #fce7f3);
    color: #be185d;
    border: 1px solid #f9a8d4;
  }
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--background-secondary);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform var(--transition-slow);
  }

  &:hover {
    border-color: var(--primary-color);
    background: var(--background-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);

    &::before {
      transform: translateX(100%);
    }
  }

  &.drag-over {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--background-primary), #eff6ff);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
  }
}

.photo-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;

  .photo-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--background-secondary);
    transition: all var(--transition-normal);

    &:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-lg);
    }

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: var(--radius-lg);
      border: 2px solid var(--border-light);
      transition: all var(--transition-normal);
    }

    .photo-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
      opacity: 0;
      transition: opacity var(--transition-normal);
      display: flex;
      align-items: flex-end;
      padding: 0.5rem;

      .photo-name {
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    &:hover .photo-overlay {
      opacity: 1;
    }
  }
}

// Advanced UI Components
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gradient-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

.floating-action-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
  }

  &:active {
    transform: scale(0.95);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.slide-in-from-right {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// Advanced Data Grid Styling
.advanced-data-grid {
  .MuiDataGrid-root {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--background-primary);

    .MuiDataGrid-columnHeaders {
      background: linear-gradient(135deg, var(--background-secondary), var(--background-tertiary));
      border-bottom: 2px solid var(--border-color);

      .MuiDataGrid-columnHeader {
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .MuiDataGrid-row {
      transition: all var(--transition-fast);

      &:hover {
        background: var(--background-secondary);
        transform: translateX(2px);
      }

      &.Mui-selected {
        background: linear-gradient(135deg, #eff6ff, #dbeafe);
      }
    }

    .MuiDataGrid-cell {
      border-bottom: 1px solid var(--border-light);

      &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .main-content {
    padding: 1.5rem;
  }

  .form-container {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .card-container {
    padding: 1rem;
  }

  .form-container {
    margin: 0.5rem;
    padding: 1rem;
  }

  .floating-action-button {
    bottom: 1rem;
    right: 1rem;
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .dashboard-grid {
    gap: 0.75rem;
  }

  .photo-preview {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;

    .photo-item img {
      height: 80px;
    }
  }
}
