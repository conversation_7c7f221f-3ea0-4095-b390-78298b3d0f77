@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

[data-theme="dark"] {
  --primary-color: #3b82f6;
  --primary-dark: #2563eb;
  --primary-light: #60a5fa;
  --secondary-color: #64748b;
  --success-color: #22c55e;
  --warning-color: #fbbf24;
  --error-color: #f87171;

  // Dark backgrounds with better contrast
  --background-primary: #0f172a;
  --background-secondary: #1e293b;
  --background-tertiary: #334155;
  --background-quaternary: #475569;
  --background-overlay: rgba(15, 23, 42, 0.95);
  --background-glass: rgba(255, 255, 255, 0.05);
  --background-card: #1e293b;
  --background-elevated: #334155;

  // Dark text colors
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-muted: #64748b;
  --text-inverse: #0f172a;

  // Dark borders
  --border-color: #334155;
  --border-light: #475569;
  --border-dark: #64748b;
  --border-accent: #60a5fa;

  // Enhanced dark shadows
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -4px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.7), 0 8px 10px -6px rgba(0, 0, 0, 0.6);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

  // Dark theme specific gradients
  --gradient-primary: linear-gradient(135deg, #1e293b, #334155);
  --gradient-secondary: linear-gradient(135deg, #0f172a, #1e293b);
  --gradient-accent: linear-gradient(135deg, #3b82f6, #60a5fa);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-tertiary) 100%);
  color: var(--text-primary);
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all var(--transition-normal);
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

[data-theme="dark"] body {
  background: linear-gradient(135deg, var(--background-primary) 0%, var(--background-secondary) 50%, var(--background-tertiary) 100%);

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(34, 197, 94, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  position: relative;
}

.main-content {
  flex: 1;
  padding: 2rem;
  background: var(--background-secondary);
  transition: all var(--transition-normal);

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.card-container {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);

    &::before {
      opacity: 1;
    }
  }
}

// Dark theme specific card styling
[data-theme="dark"] .card-container {
  background: var(--background-card);
  border-color: var(--border-color);
  box-shadow: var(--shadow-lg);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover {
    background: var(--background-elevated);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
    border-color: var(--border-accent);

    &::after {
      opacity: 1;
    }
  }
}

.form-container {
  max-width: 700px;
  margin: 0 auto;
  background: var(--background-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--success-color));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  }

  @media (max-width: 768px) {
    margin: 1rem;
    padding: 1.5rem;
  }
}

// Dark theme form styling
[data-theme="dark"] .form-container {
  background: var(--background-card);
  border-color: var(--border-color);
  box-shadow: var(--shadow-xl);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    pointer-events: none;
    border-radius: var(--radius-xl);
  }
}

.status-chip {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.online {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
  }

  &.offline {
    background: linear-gradient(135deg, #fef2f2, #fecaca);
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  &.maintenance {
    background: linear-gradient(135deg, #fefbeb, #fed7aa);
    color: #92400e;
    border: 1px solid #fdba74;
  }

  &.active {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
    border: 1px solid #93c5fd;
  }

  &.expired {
    background: linear-gradient(135deg, #fdf2f8, #fce7f3);
    color: #be185d;
    border: 1px solid #f9a8d4;
  }
}

// Dark theme status chips
[data-theme="dark"] .status-chip {
  &.online {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(34, 197, 94, 0.3));
    color: #4ade80;
    border: 1px solid rgba(34, 197, 94, 0.4);
    box-shadow: 0 0 10px rgba(34, 197, 94, 0.2);
  }

  &.offline {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.3));
    color: #f87171;
    border: 1px solid rgba(239, 68, 68, 0.4);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
  }

  &.maintenance {
    background: linear-gradient(135deg, rgba(251, 191, 36, 0.2), rgba(251, 191, 36, 0.3));
    color: #fbbf24;
    border: 1px solid rgba(251, 191, 36, 0.4);
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.2);
  }

  &.active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(59, 130, 246, 0.3));
    color: #60a5fa;
    border: 1px solid rgba(59, 130, 246, 0.4);
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
  }

  &.expired {
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(236, 72, 153, 0.3));
    color: #f472b6;
    border: 1px solid rgba(236, 72, 153, 0.4);
    box-shadow: 0 0 10px rgba(236, 72, 153, 0.2);
  }
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  background: var(--background-secondary);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform var(--transition-slow);
  }

  &:hover {
    border-color: var(--primary-color);
    background: var(--background-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);

    &::before {
      transform: translateX(100%);
    }
  }

  &.drag-over {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, var(--background-primary), #eff6ff);
    transform: scale(1.02);
    box-shadow: var(--shadow-lg);
  }
}

// Dark theme file upload area
[data-theme="dark"] .file-upload-area {
  background: var(--background-secondary);
  border-color: var(--border-color);

  &::before {
    background: linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
  }

  &:hover {
    background: var(--background-tertiary);
    border-color: var(--border-accent);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
  }

  &.drag-over {
    background: linear-gradient(135deg, var(--background-tertiary), rgba(59, 130, 246, 0.1));
    border-color: var(--border-accent);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
  }
}

.photo-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;

  .photo-item {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--background-secondary);
    transition: all var(--transition-normal);

    &:hover {
      transform: scale(1.05);
      box-shadow: var(--shadow-lg);
    }

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: var(--radius-lg);
      border: 2px solid var(--border-light);
      transition: all var(--transition-normal);
    }

    .photo-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
      opacity: 0;
      transition: opacity var(--transition-normal);
      display: flex;
      align-items: flex-end;
      padding: 0.5rem;

      .photo-name {
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }

    &:hover .photo-overlay {
      opacity: 1;
    }
  }
}

// Dark theme photo preview
[data-theme="dark"] .photo-preview {
  .photo-item {
    background: var(--background-tertiary);

    &:hover {
      box-shadow: var(--shadow-xl), var(--shadow-glow);
    }

    img {
      border-color: var(--border-color);

      &:hover {
        border-color: var(--border-accent);
      }
    }

    .photo-overlay {
      background: linear-gradient(to bottom, transparent 0%, rgba(15, 23, 42, 0.9) 100%);

      .photo-name {
        color: var(--text-primary);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
    }
  }
}

// Advanced UI Components
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gradient-button {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }
}

.floating-action-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  z-index: 1000;

  &:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
  }

  &:active {
    transform: scale(0.95);
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-md);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.slide-in-from-right {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// Dark theme specific animations
[data-theme="dark"] {
  @keyframes darkGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes darkPulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }

  .pulse-glow {
    animation: darkGlow 2s infinite;
  }

  .dark-pulse {
    animation: darkPulse 2s infinite;
  }
}

// Advanced Data Grid Styling
.advanced-data-grid {
  .MuiDataGrid-root {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--background-primary);

    .MuiDataGrid-columnHeaders {
      background: linear-gradient(135deg, var(--background-secondary), var(--background-tertiary));
      border-bottom: 2px solid var(--border-color);

      .MuiDataGrid-columnHeader {
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .MuiDataGrid-row {
      transition: all var(--transition-fast);

      &:hover {
        background: var(--background-secondary);
        transform: translateX(2px);
      }

      &.Mui-selected {
        background: linear-gradient(135deg, #eff6ff, #dbeafe);
      }
    }

    .MuiDataGrid-cell {
      border-bottom: 1px solid var(--border-light);

      &:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: -2px;
      }
    }
  }
}

// Dark theme Material-UI component overrides
[data-theme="dark"] {
  .MuiButton-root {
    &.MuiButton-contained {
      background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
      box-shadow: var(--shadow-md);

      &:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        box-shadow: var(--shadow-lg), var(--shadow-glow);
      }
    }

    &.MuiButton-outlined {
      border-color: var(--border-color);
      color: var(--text-primary);

      &:hover {
        border-color: var(--border-accent);
        background: rgba(59, 130, 246, 0.1);
      }
    }
  }

  .MuiTextField-root {
    .MuiOutlinedInput-root {
      background: var(--background-secondary);

      .MuiOutlinedInput-notchedOutline {
        border-color: var(--border-color);
      }

      &:hover .MuiOutlinedInput-notchedOutline {
        border-color: var(--border-accent);
      }

      &.Mui-focused .MuiOutlinedInput-notchedOutline {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      }
    }

    .MuiInputLabel-root {
      color: var(--text-secondary);

      &.Mui-focused {
        color: var(--primary-color);
      }
    }
  }

  .MuiSelect-root {
    background: var(--background-secondary);

    .MuiOutlinedInput-notchedOutline {
      border-color: var(--border-color);
    }

    &:hover .MuiOutlinedInput-notchedOutline {
      border-color: var(--border-accent);
    }
  }

  .MuiDialog-paper {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-xl);
  }

  .MuiDialogTitle-root {
    background: linear-gradient(135deg, var(--background-secondary), var(--background-tertiary));
    border-bottom: 1px solid var(--border-color);
  }

  .MuiChip-root {
    background: var(--background-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);

    &:hover {
      background: var(--background-quaternary);
    }
  }

  .MuiSwitch-root {
    .MuiSwitch-switchBase {
      &.Mui-checked {
        color: var(--primary-color);

        & + .MuiSwitch-track {
          background: var(--primary-color);
          opacity: 0.5;
        }
      }
    }

    .MuiSwitch-track {
      background: var(--border-color);
    }
  }
}

// Responsive Design
@media (max-width: 1024px) {
  .main-content {
    padding: 1.5rem;
  }

  .form-container {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }

  .card-container {
    padding: 1rem;
  }

  .form-container {
    margin: 0.5rem;
    padding: 1rem;
  }

  .floating-action-button {
    bottom: 1rem;
    right: 1rem;
    width: 48px;
    height: 48px;
  }
}

@media (max-width: 480px) {
  .dashboard-grid {
    gap: 0.75rem;
  }

  .photo-preview {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;

    .photo-item img {
      height: 80px;
    }
  }
}
