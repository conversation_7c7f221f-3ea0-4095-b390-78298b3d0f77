@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #3b82f6;
  --success-color: #22c55e;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --border-color: #e2e8f0;
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --transition-normal: 0.3s ease-in-out;
}

[data-theme="dark"] {
  --primary-color: #3b82f6;
  --background-primary: #000000;
  --background-secondary: #111111;
  --background-card: #111111;
  --text-primary: #ffffff;
  --text-secondary: #e5e5e5;
  --border-color: #333333;
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 1);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.5);
}

body {
  font-family: 'Inter', sans-serif;
  background: var(--background-secondary);
  color: var(--text-primary);
  margin: 0;
  transition: all var(--transition-normal);
}

[data-theme="dark"] body {
  background: #000000;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem;
  background: var(--background-secondary);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 1.5rem;
}

.card-container {
  background: var(--background-primary);
  border-radius: var(--radius-xl);
  padding: 1.5rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-color);
  transition: all var(--transition-normal);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}


[data-theme="dark"] .card-container {
  background: var(--background-card);
  border-color: var(--border-color);
  box-shadow: var(--shadow-lg);
}

.form-container {
  max-width: 700px;
  margin: 0 auto;
  background: var(--background-primary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}


[data-theme="dark"] .form-container {
  background: var(--background-card);
  border-color: var(--border-color);
}

.status-chip {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;

  &.online {
    background: #dcfce7;
    color: #166534;
  }

  &.offline {
    background: #fef2f2;
    color: #991b1b;
  }

  &.maintenance {
    background: #fefbeb;
    color: #92400e;
  }

  &.active {
    background: #dbeafe;
    color: #1e40af;
  }

  &.expired {
    background: #fdf2f8;
    color: #be185d;
  }
}


[data-theme="dark"] .status-chip {
  &.online {
    background: rgba(34, 197, 94, 0.2);
    color: #4ade80;
  }

  &.offline {
    background: rgba(239, 68, 68, 0.2);
    color: #f87171;
  }

  &.maintenance {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
  }

  &.active {
    background: rgba(59, 130, 246, 0.2);
    color: #60a5fa;
  }

  &.expired {
    background: rgba(236, 72, 153, 0.2);
    color: #f472b6;
  }
}

.file-upload-area {
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  padding: 3rem 2rem;
  text-align: center;
  cursor: pointer;
  background: var(--background-secondary);

  &:hover {
    border-color: var(--primary-color);
  }
}


[data-theme="dark"] .file-upload-area {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

.photo-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;

  .photo-item {
    border-radius: var(--radius-lg);
    overflow: hidden;

    img {
      width: 100%;
      height: 120px;
      object-fit: cover;
      border-radius: var(--radius-lg);
    }
  }
}







