import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Paper,
  IconButton,
  ImageList,
  ImageListItem,
  ImageListItemBar,
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  PhotoCamera,
  CheckCircle,
  Warning,
  Error,
  Info,
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import {
  addAlert,
  updateAlert,
  deleteAlert,
  resolveAlert,
  addAlertPhoto,
  removeAlertPhoto,
} from '../store/slices/alertsSlice'

const AlertsPhotoLogs = () => {
  const dispatch = useDispatch()
  const { alerts } = useSelector(state => state.alerts)
  const { devices } = useSelector(state => state.devices)
  const { user } = useSelector(state => state.auth)
  
  const [dialogOpen, setDialogOpen] = useState(false)
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false)
  const [resolveDialogOpen, setResolveDialogOpen] = useState(false)
  const [editingAlert, setEditingAlert] = useState(null)
  const [selectedAlert, setSelectedAlert] = useState(null)
  const [resolution, setResolution] = useState('')
  const [formData, setFormData] = useState({
    deviceId: '',
    deviceType: '',
    facility: '',
    alertType: 'Maintenance Required',
    severity: 'Medium',
    message: '',
    assignedTo: '',
    notes: '',
  })

  const handleAddAlert = () => {
    setEditingAlert(null)
    setFormData({
      deviceId: '',
      deviceType: '',
      facility: '',
      alertType: 'Maintenance Required',
      severity: 'Medium',
      message: '',
      assignedTo: '',
      notes: '',
    })
    setDialogOpen(true)
  }

  const handleEditAlert = (alert) => {
    setEditingAlert(alert)
    setFormData(alert)
    setDialogOpen(true)
  }

  const handleSaveAlert = () => {
    if (editingAlert) {
      dispatch(updateAlert({ ...formData, id: editingAlert.id }))
    } else {
      dispatch(addAlert(formData))
    }
    setDialogOpen(false)
  }

  const handleDeleteAlert = (alertId) => {
    if (window.confirm('Are you sure you want to delete this alert?')) {
      dispatch(deleteAlert(alertId))
    }
  }

  const handleResolveAlert = () => {
    if (selectedAlert && resolution.trim()) {
      dispatch(resolveAlert({
        alertId: selectedAlert.id,
        resolution: resolution.trim()
      }))
      setResolveDialogOpen(false)
      setResolution('')
      setSelectedAlert(null)
    }
  }

  const handlePhotoUpload = (alertId, event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        dispatch(addAlertPhoto({
          alertId,
          photo: {
            name: file.name,
            url: e.target.result,
            uploadDate: new Date().toISOString(),
          }
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'High': return 'error'
      case 'Medium': return 'warning'
      case 'Low': return 'info'
      default: return 'default'
    }
  }

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'High': return <Error />
      case 'Medium': return <Warning />
      case 'Low': return <Info />
      default: return <Info />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Open': return 'error'
      case 'In Progress': return 'warning'
      case 'Resolved': return 'success'
      default: return 'default'
    }
  }

  const columns = [
    { field: 'id', headerName: 'Alert ID', width: 120 },
    { field: 'deviceId', headerName: 'Device ID', width: 120 },
    { field: 'deviceType', headerName: 'Device Type', width: 150 },
    { field: 'facility', headerName: 'Facility', width: 150 },
    { field: 'alertType', headerName: 'Alert Type', width: 150 },
    {
      field: 'severity',
      headerName: 'Severity',
      width: 120,
      renderCell: (params) => (
        <Chip
          icon={getSeverityIcon(params.value)}
          label={params.value}
          color={getSeverityColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    { field: 'assignedTo', headerName: 'Assigned To', width: 130 },
    { field: 'createdDate', headerName: 'Created', width: 120 },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEditAlert(params.row)}
            disabled={!user?.permissions.includes('write')}
          >
            <Edit />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedAlert(params.row)
              setPhotoDialogOpen(true)
            }}
          >
            <PhotoCamera />
          </IconButton>
          {params.row.status !== 'Resolved' && (
            <IconButton
              size="small"
              onClick={() => {
                setSelectedAlert(params.row)
                setResolveDialogOpen(true)
              }}
              disabled={!user?.permissions.includes('write')}
            >
              <CheckCircle />
            </IconButton>
          )}
          <IconButton
            size="small"
            onClick={() => handleDeleteAlert(params.row.id)}
            disabled={!user?.permissions.includes('delete')}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ]

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Alerts & Photo Logs
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleAddAlert}
          disabled={!user?.permissions.includes('write')}
        >
          New Alert
        </Button>
      </Box>

      <Paper>
        <DataGrid
          rows={alerts}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          autoHeight
          disableRowSelectionOnClick
        />
      </Paper>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingAlert ? 'Edit Alert' : 'New Alert'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={formData.deviceId}
                  onChange={(e) => {
                    const device = devices.find(d => d.id === e.target.value)
                    setFormData({
                      ...formData,
                      deviceId: e.target.value,
                      deviceType: device?.type || '',
                      facility: device?.facility || '',
                    })
                  }}
                >
                  {devices.map((device) => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.id} - {device.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Alert Type</InputLabel>
                <Select
                  value={formData.alertType}
                  onChange={(e) => setFormData({ ...formData, alertType: e.target.value })}
                >
                  <MenuItem value="Maintenance Required">Maintenance Required</MenuItem>
                  <MenuItem value="Battery Low">Battery Low</MenuItem>
                  <MenuItem value="Installation Issue">Installation Issue</MenuItem>
                  <MenuItem value="Calibration Error">Calibration Error</MenuItem>
                  <MenuItem value="Hardware Failure">Hardware Failure</MenuItem>
                  <MenuItem value="Software Issue">Software Issue</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Severity</InputLabel>
                <Select
                  value={formData.severity}
                  onChange={(e) => setFormData({ ...formData, severity: e.target.value })}
                >
                  <MenuItem value="High">High</MenuItem>
                  <MenuItem value="Medium">Medium</MenuItem>
                  <MenuItem value="Low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Assigned To"
                value={formData.assignedTo}
                onChange={(e) => setFormData({ ...formData, assignedTo: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Message"
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveAlert} variant="contained">
            {editingAlert ? 'Update' : 'Create'} Alert
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={photoDialogOpen} onClose={() => setPhotoDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Photo Logs - {selectedAlert?.id}</DialogTitle>
        <DialogContent>
          {selectedAlert && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Device: {selectedAlert.deviceType} ({selectedAlert.deviceId})
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Alert: {selectedAlert.message}
              </Typography>

              <Box mt={3} mb={2}>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="alert-photo-upload"
                  type="file"
                  onChange={(e) => handlePhotoUpload(selectedAlert.id, e)}
                />
                <label htmlFor="alert-photo-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<PhotoCamera />}
                    disabled={!user?.permissions.includes('write')}
                  >
                    Upload Photo
                  </Button>
                </label>
              </Box>

              {selectedAlert.photos.length > 0 ? (
                <ImageList cols={3} rowHeight={200}>
                  {selectedAlert.photos.map((photo, index) => (
                    <ImageListItem key={index}>
                      <img
                        src={photo.url}
                        alt={photo.name}
                        loading="lazy"
                        style={{ height: 200, objectFit: 'cover' }}
                      />
                      <ImageListItemBar
                        title={photo.name}
                        subtitle={new Date(photo.uploadDate).toLocaleDateString()}
                        actionIcon={
                          <IconButton
                            sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                            onClick={() => dispatch(removeAlertPhoto({ alertId: selectedAlert.id, photoIndex: index }))}
                            disabled={!user?.permissions.includes('write')}
                          >
                            <Delete />
                          </IconButton>
                        }
                      />
                    </ImageListItem>
                  ))}
                </ImageList>
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No photos uploaded yet
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPhotoDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      <Dialog open={resolveDialogOpen} onClose={() => setResolveDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Resolve Alert - {selectedAlert?.id}</DialogTitle>
        <DialogContent>
          {selectedAlert && (
            <Box>
              <Typography variant="body1" gutterBottom>
                <strong>Device:</strong> {selectedAlert.deviceType} ({selectedAlert.deviceId})
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Alert:</strong> {selectedAlert.message}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Severity:</strong>
                <Chip
                  icon={getSeverityIcon(selectedAlert.severity)}
                  label={selectedAlert.severity}
                  color={getSeverityColor(selectedAlert.severity)}
                  size="small"
                  sx={{ ml: 1 }}
                />
              </Typography>

              <TextField
                fullWidth
                label="Resolution Details"
                multiline
                rows={4}
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                sx={{ mt: 2 }}
                placeholder="Describe how this alert was resolved..."
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResolveDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleResolveAlert}
            variant="contained"
            disabled={!resolution.trim()}
          >
            Resolve Alert
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AlertsPhotoLogs
