import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  alerts: [
    {
      id: 'ALT001',
      deviceId: 'DEV002',
      deviceType: 'Patient Monitor',
      facility: 'General Hospital',
      alertType: 'Maintenance Required',
      severity: 'High',
      message: '<PERSON><PERSON> showing calibration errors. Immediate attention required.',
      createdDate: '2024-06-25',
      status: 'Open',
      assignedTo: '<PERSON>',
      photos: [],
      notes: '<PERSON><PERSON> reported multiple calibration failures during routine checks.',
      resolvedDate: null,
      resolution: null,
    },
    {
      id: 'ALT002',
      deviceId: 'DEV001',
      deviceType: 'Ventilator',
      facility: 'City Hospital',
      alertType: 'Battery Low',
      severity: 'Medium',
      message: 'Battery level below 20%. Schedule replacement soon.',
      createdDate: '2024-06-20',
      status: 'In Progress',
      assignedTo: '<PERSON>',
      photos: [],
      notes: 'Battery performance degrading. Replacement scheduled for next week.',
      resolvedDate: null,
      resolution: null,
    },
    {
      id: 'ALT003',
      deviceId: 'DEV003',
      deviceType: 'X-Ray Machine',
      facility: 'City Hospital',
      alertType: 'Installation Issue',
      severity: 'Low',
      message: 'Minor alignment issue detected during installation.',
      createdDate: '2024-06-18',
      status: 'Resolved',
      assignedTo: '<PERSON>',
      photos: [],
      notes: 'Alignment corrected during installation process.',
      resolvedDate: '2024-06-19',
      resolution: 'Realigned X-ray tube and verified proper positioning.',
    },
  ],
  loading: false,
  error: null,
}

const alertsSlice = createSlice({
  name: 'alerts',
  initialState,
  reducers: {
    addAlert: (state, action) => {
      state.alerts.push({
        ...action.payload,
        id: `ALT${String(state.alerts.length + 1).padStart(3, '0')}`,
        createdDate: new Date().toISOString().split('T')[0],
        status: 'Open',
      })
    },
    updateAlert: (state, action) => {
      const index = state.alerts.findIndex(alert => alert.id === action.payload.id)
      if (index !== -1) {
        state.alerts[index] = { ...state.alerts[index], ...action.payload }
      }
    },
    deleteAlert: (state, action) => {
      state.alerts = state.alerts.filter(alert => alert.id !== action.payload)
    },
    resolveAlert: (state, action) => {
      const { alertId, resolution } = action.payload
      const alert = state.alerts.find(a => a.id === alertId)
      if (alert) {
        alert.status = 'Resolved'
        alert.resolvedDate = new Date().toISOString().split('T')[0]
        alert.resolution = resolution
      }
    },
    addAlertPhoto: (state, action) => {
      const { alertId, photo } = action.payload
      const alert = state.alerts.find(a => a.id === alertId)
      if (alert) {
        alert.photos.push(photo)
      }
    },
    removeAlertPhoto: (state, action) => {
      const { alertId, photoIndex } = action.payload
      const alert = state.alerts.find(a => a.id === alertId)
      if (alert) {
        alert.photos.splice(photoIndex, 1)
      }
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    setError: (state, action) => {
      state.error = action.payload
    },
  },
})

export const {
  addAlert,
  updateAlert,
  deleteAlert,
  resolveAlert,
  addAlertPhoto,
  removeAlertPhoto,
  setLoading,
  setError,
} = alertsSlice.actions

export default alertsSlice.reducer
