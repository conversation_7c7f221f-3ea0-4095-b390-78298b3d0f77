import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  user: {
    id: 'USR001',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'Admin',
    permissions: ['read', 'write', 'delete', 'export'],
  },
  isAuthenticated: true,
  loading: false,
  error: null,
}

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true
      state.error = null
    },
    loginSuccess: (state, action) => {
      state.loading = false
      state.isAuthenticated = true
      state.user = action.payload
      state.error = null
    },
    loginFailure: (state, action) => {
      state.loading = false
      state.isAuthenticated = false
      state.user = null
      state.error = action.payload
    },
    logout: (state) => {
      state.isAuthenticated = false
      state.user = null
      state.error = null
    },
    switchRole: (state, action) => {
      if (state.user) {
        state.user.role = action.payload
        if (action.payload === 'Technician') {
          state.user.permissions = ['read', 'write']
        } else {
          state.user.permissions = ['read', 'write', 'delete', 'export']
        }
      }
    },
    clearError: (state) => {
      state.error = null
    },
  },
})

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  switchRole,
  clearError,
} = authSlice.actions

export default authSlice.reducer
