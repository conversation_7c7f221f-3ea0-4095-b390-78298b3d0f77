import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  mode: 'light',
  primaryColor: '#1976d2',
  secondaryColor: '#dc004e',
}

const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    toggleTheme: (state) => {
      state.mode = state.mode === 'light' ? 'dark' : 'light'
    },
    setThemeMode: (state, action) => {
      state.mode = action.payload
    },
    setPrimaryColor: (state, action) => {
      state.primaryColor = action.payload
    },
    setSecondaryColor: (state, action) => {
      state.secondaryColor = action.payload
    },
    resetTheme: (state) => {
      state.mode = 'light'
      state.primaryColor = '#1976d2'
      state.secondaryColor = '#dc004e'
    },
  },
})

export const {
  toggleTheme,
  setThemeMode,
  setPrimaryColor,
  setSecondaryColor,
  resetTheme,
} = themeSlice.actions

export default themeSlice.reducer
