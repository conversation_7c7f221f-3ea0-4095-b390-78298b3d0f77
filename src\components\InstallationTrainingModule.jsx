import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Paper,
  Divider,
  LinearProgress,
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  PhotoCamera,
  CheckCircle,
  RadioButtonUnchecked,
  Upload,
  Person,
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import {
  addInstallation,
  updateInstallation,
  deleteInstallation,
  updateChecklist,
  updateTrainingForm,
  addUnboxingPhoto,
} from '../store/slices/installationsSlice'

const InstallationTrainingModule = () => {
  const dispatch = useDispatch()
  const { installations } = useSelector(state => state.installations)
  const { devices } = useSelector(state => state.devices)
  const { user } = useSelector(state => state.auth)
  
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingInstallation, setEditingInstallation] = useState(null)
  const [checklistDialogOpen, setChecklistDialogOpen] = useState(false)
  const [trainingDialogOpen, setTrainingDialogOpen] = useState(false)
  const [selectedInstallation, setSelectedInstallation] = useState(null)
  const [formData, setFormData] = useState({
    deviceId: '',
    deviceType: '',
    facility: '',
    installationDate: '',
    technician: '',
    status: 'In Progress',
    notes: '',
  })

  const handleAddInstallation = () => {
    setEditingInstallation(null)
    setFormData({
      deviceId: '',
      deviceType: '',
      facility: '',
      installationDate: '',
      technician: '',
      status: 'In Progress',
      notes: '',
    })
    setDialogOpen(true)
  }

  const handleEditInstallation = (installation) => {
    setEditingInstallation(installation)
    setFormData(installation)
    setDialogOpen(true)
  }

  const handleSaveInstallation = () => {
    const installationData = {
      ...formData,
      checklist: {
        deviceInspection: false,
        functionalTesting: false,
        calibration: false,
        userTraining: false,
        documentation: false,
      },
      trainingForm: {
        trainedPersonnel: [],
        trainingDate: null,
        trainingDuration: null,
        competencyAssessment: null,
      },
      unboxingPhotos: [],
    }

    if (editingInstallation) {
      dispatch(updateInstallation({ ...installationData, id: editingInstallation.id }))
    } else {
      dispatch(addInstallation(installationData))
    }
    setDialogOpen(false)
  }

  const handleDeleteInstallation = (installationId) => {
    if (window.confirm('Are you sure you want to delete this installation record?')) {
      dispatch(deleteInstallation(installationId))
    }
  }

  const handleChecklistUpdate = (installationId, checklistItem, value) => {
    dispatch(updateChecklist({
      installationId,
      checklist: { [checklistItem]: value }
    }))
  }

  const handleTrainingFormUpdate = (installationId, field, value) => {
    dispatch(updateTrainingForm({
      installationId,
      trainingForm: { [field]: value }
    }))
  }

  const handlePhotoUpload = (installationId, event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        dispatch(addUnboxingPhoto({
          installationId,
          photo: {
            name: file.name,
            url: e.target.result,
            uploadDate: new Date().toISOString(),
          }
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const getCompletionPercentage = (installation) => {
    const checklist = installation.checklist
    const completed = Object.values(checklist).filter(Boolean).length
    const total = Object.keys(checklist).length
    return Math.round((completed / total) * 100)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success'
      case 'In Progress': return 'warning'
      case 'Pending': return 'default'
      default: return 'default'
    }
  }

  const columns = [
    { field: 'id', headerName: 'Installation ID', width: 130 },
    { field: 'deviceId', headerName: 'Device ID', width: 120 },
    { field: 'deviceType', headerName: 'Device Type', width: 150 },
    { field: 'facility', headerName: 'Facility', width: 150 },
    { field: 'installationDate', headerName: 'Installation Date', width: 140 },
    { field: 'technician', headerName: 'Technician', width: 130 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value)}
          size="small"
        />
      ),
    },
    {
      field: 'completion',
      headerName: 'Progress',
      width: 120,
      renderCell: (params) => {
        const percentage = getCompletionPercentage(params.row)
        return (
          <Box sx={{ width: '100%' }}>
            <LinearProgress variant="determinate" value={percentage} />
            <Typography variant="caption">{percentage}%</Typography>
          </Box>
        )
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEditInstallation(params.row)}
            disabled={!user?.permissions.includes('write')}
          >
            <Edit />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedInstallation(params.row)
              setChecklistDialogOpen(true)
            }}
          >
            <CheckCircle />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedInstallation(params.row)
              setTrainingDialogOpen(true)
            }}
          >
            <Person />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDeleteInstallation(params.row.id)}
            disabled={!user?.permissions.includes('delete')}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ]

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Installation & Training Module
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleAddInstallation}
          disabled={!user?.permissions.includes('write')}
        >
          New Installation
        </Button>
      </Box>

      <Paper>
        <DataGrid
          rows={installations}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          autoHeight
          disableRowSelectionOnClick
        />
      </Paper>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingInstallation ? 'Edit Installation' : 'New Installation'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={formData.deviceId}
                  onChange={(e) => {
                    const device = devices.find(d => d.id === e.target.value)
                    setFormData({
                      ...formData,
                      deviceId: e.target.value,
                      deviceType: device?.type || '',
                      facility: device?.facility || '',
                    })
                  }}
                >
                  {devices.map((device) => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.id} - {device.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Device Type"
                value={formData.deviceType}
                onChange={(e) => setFormData({ ...formData, deviceType: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Facility"
                value={formData.facility}
                onChange={(e) => setFormData({ ...formData, facility: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Installation Date"
                type="date"
                value={formData.installationDate}
                onChange={(e) => setFormData({ ...formData, installationDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Technician"
                value={formData.technician}
                onChange={(e) => setFormData({ ...formData, technician: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                >
                  <MenuItem value="Pending">Pending</MenuItem>
                  <MenuItem value="In Progress">In Progress</MenuItem>
                  <MenuItem value="Completed">Completed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                multiline
                rows={3}
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveInstallation} variant="contained">
            {editingInstallation ? 'Update' : 'Create'} Installation
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={checklistDialogOpen} onClose={() => setChecklistDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Installation Checklist - {selectedInstallation?.id}</DialogTitle>
        <DialogContent>
          {selectedInstallation && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Device: {selectedInstallation.deviceType} ({selectedInstallation.deviceId})
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <FormGroup>
                {Object.entries(selectedInstallation.checklist).map(([key, value]) => (
                  <FormControlLabel
                    key={key}
                    control={
                      <Checkbox
                        checked={value}
                        onChange={(e) => handleChecklistUpdate(selectedInstallation.id, key, e.target.checked)}
                        disabled={!user?.permissions.includes('write')}
                      />
                    }
                    label={key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  />
                ))}
              </FormGroup>

              <Box mt={3}>
                <Typography variant="h6" gutterBottom>
                  Unboxing Photos
                </Typography>
                <input
                  accept="image/*"
                  style={{ display: 'none' }}
                  id="photo-upload"
                  type="file"
                  onChange={(e) => handlePhotoUpload(selectedInstallation.id, e)}
                />
                <label htmlFor="photo-upload">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<PhotoCamera />}
                    disabled={!user?.permissions.includes('write')}
                  >
                    Upload Photo
                  </Button>
                </label>

                <Grid container spacing={2} sx={{ mt: 2 }}>
                  {selectedInstallation.unboxingPhotos.map((photo, index) => (
                    <Grid item xs={6} sm={4} md={3} key={index}>
                      <Card>
                        <img
                          src={photo.url}
                          alt={photo.name}
                          style={{ width: '100%', height: 120, objectFit: 'cover' }}
                        />
                        <CardContent sx={{ p: 1 }}>
                          <Typography variant="caption" noWrap>
                            {photo.name}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setChecklistDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      <Dialog open={trainingDialogOpen} onClose={() => setTrainingDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Training Form - {selectedInstallation?.id}</DialogTitle>
        <DialogContent>
          {selectedInstallation && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Device: {selectedInstallation.deviceType} ({selectedInstallation.deviceId})
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Trained Personnel (comma separated)"
                    value={selectedInstallation.trainingForm.trainedPersonnel.join(', ')}
                    onChange={(e) => handleTrainingFormUpdate(
                      selectedInstallation.id,
                      'trainedPersonnel',
                      e.target.value.split(',').map(name => name.trim()).filter(Boolean)
                    )}
                    disabled={!user?.permissions.includes('write')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Training Date"
                    type="date"
                    value={selectedInstallation.trainingForm.trainingDate || ''}
                    onChange={(e) => handleTrainingFormUpdate(selectedInstallation.id, 'trainingDate', e.target.value)}
                    InputLabelProps={{ shrink: true }}
                    disabled={!user?.permissions.includes('write')}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Training Duration"
                    value={selectedInstallation.trainingForm.trainingDuration || ''}
                    onChange={(e) => handleTrainingFormUpdate(selectedInstallation.id, 'trainingDuration', e.target.value)}
                    disabled={!user?.permissions.includes('write')}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Competency Assessment</InputLabel>
                    <Select
                      value={selectedInstallation.trainingForm.competencyAssessment || ''}
                      onChange={(e) => handleTrainingFormUpdate(selectedInstallation.id, 'competencyAssessment', e.target.value)}
                      disabled={!user?.permissions.includes('write')}
                    >
                      <MenuItem value="Passed">Passed</MenuItem>
                      <MenuItem value="Failed">Failed</MenuItem>
                      <MenuItem value="Pending">Pending</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTrainingDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default InstallationTrainingModule
