import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Switch,
  FormControlLabel,
  Menu,
  MenuItem,
  Badge,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard,
  Devices,
  Build,
  Assignment,
  Description,
  Warning,
  Brightness4,
  Brightness7,
  AccountCircle,
  QrCodeScanner,
} from '@mui/icons-material'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'

import DeviceInventoryDashboard from './components/DeviceInventoryDashboard'
import InstallationTrainingModule from './components/InstallationTrainingModule'
import ServiceVisitLogs from './components/ServiceVisitLogs'
import AmcCmcTracker from './components/AmcCmcTracker'
import AlertsPhotoLogs from './components/AlertsPhotoLogs'
import QRCodeScanner from './components/QRCodeScanner'

import { toggleTheme, setThemeMode } from './store/slices/themeSlice'
import { switchRole } from './store/slices/authSlice'

function App() {
  const dispatch = useDispatch()
  const { mode, primaryColor, secondaryColor } = useSelector(state => state.theme)
  const { user } = useSelector(state => state.auth)
  const { alerts } = useSelector(state => state.alerts)

  const [drawerOpen, setDrawerOpen] = useState(false)
  const [currentView, setCurrentView] = useState('dashboard')
  const [anchorEl, setAnchorEl] = useState(null)

  const darkMode = mode === 'dark'

  useEffect(() => {
    document.body.setAttribute('data-theme', mode)
    return () => {
      document.body.removeAttribute('data-theme')
    }
  }, [mode])

  const theme = createTheme({
    palette: {
      mode,
      primary: { main: primaryColor },
      secondary: { main: secondaryColor },
      background: {
        default: mode === 'dark' ? '#000000' : '#f8fafc',
        paper: mode === 'dark' ? '#111111' : '#ffffff',
      },
    },
    components: {
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'dark' ? '#111111' : '#ffffff',
          },
        },
      },
    },
  })

  const openAlerts = alerts.filter(alert => alert.status === 'Open').length

  const menuItems = [
    { id: 'dashboard', label: 'Device Inventory', icon: <Dashboard />, roles: ['Admin', 'Technician'] },
    { id: 'installations', label: 'Installation & Training', icon: <Build />, roles: ['Admin', 'Technician'] },
    { id: 'service', label: 'Service Visit Logs', icon: <Assignment />, roles: ['Admin', 'Technician'] },
    { id: 'amc-cmc', label: 'AMC/CMC Tracker', icon: <Description />, roles: ['Admin'] },
    { id: 'alerts', label: 'Alerts & Photo Logs', icon: <Warning />, roles: ['Admin', 'Technician'] },
    { id: 'qr-scanner', label: 'QR Code Scanner', icon: <QrCodeScanner />, roles: ['Admin', 'Technician'] },
  ]

  const filteredMenuItems = menuItems.filter(item => item.roles.includes(user?.role))
  const handleThemeToggle = () => dispatch(toggleTheme())
  const handleRoleSwitch = (role) => {
    dispatch(switchRole(role))
    setAnchorEl(null)
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DeviceInventoryDashboard />
      case 'installations':
        return <InstallationTrainingModule />
      case 'service':
        return <ServiceVisitLogs />
      case 'amc-cmc':
        return <AmcCmcTracker />
      case 'alerts':
        return <AlertsPhotoLogs />
      case 'qr-scanner':
        return <QRCodeScanner />
      default:
        return <DeviceInventoryDashboard />
    }
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box className="app">
        <AppBar
          position="fixed"
          sx={{
            background: 'linear-gradient(135deg, var(--primary-color), var(--primary-light))',
            backdropFilter: 'blur(10px)',
            borderBottom: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'var(--shadow-lg)',
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                edge="start"
                onClick={() => setDrawerOpen(true)}
                sx={{
                  mr: 2,
                  color: darkMode ? '#ffffff' : '#000000',
                  '&:hover': {
                    backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                  }
                }}
              >
                <MenuIcon />
            </IconButton>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  flexGrow: 1,
                  fontWeight: 500,
                  color: darkMode ? '#ffffff' : '#000000',
                }}
              >
                Device CRM + Inventory Management
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Badge
                badgeContent={openAlerts}
                color="error"
                sx={{
                  '& .MuiBadge-badge': {
                    animation: openAlerts > 0 ? 'pulse 2s infinite' : 'none',
                  }
                }}
              >
                <IconButton
                  sx={{
                    color: darkMode ? '#ffffff' : '#000000',
                    '&:hover': {
                      backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                    }
                  }}
                >
                  <Warning />
                </IconButton>
              </Badge>

              <FormControlLabel
                control={
                  <Switch
                    checked={mode === 'dark'}
                    onChange={handleThemeToggle}
                    icon={<Brightness7 />}
                    checkedIcon={<Brightness4 />}
                    sx={{
                      '& .MuiSwitch-switchBase': {
                        transition: 'all 0.3s ease',
                      },
                      '& .MuiSwitch-thumb': {
                        boxShadow: 'var(--shadow-md)',
                      }
                    }}
                  />
                }
                label=""
              />

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" sx={{ color: darkMode ? 'rgba(255,255,255,0.8)' : 'rgba(0,0,0,0.8)' }}>
                  {user?.role}
                </Typography>
                <IconButton
                  onClick={(e) => setAnchorEl(e.currentTarget)}
                  sx={{
                    color: darkMode ? '#ffffff' : '#000000',
                    '&:hover': {
                      backgroundColor: darkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                    }
                  }}
                >
                  <AccountCircle />
                </IconButton>
              </Box>

              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={() => setAnchorEl(null)}
              >
                <MenuItem onClick={() => handleRoleSwitch('Admin')}>
                  Switch to Admin
                </MenuItem>
                <MenuItem onClick={() => handleRoleSwitch('Technician')}>
                  Switch to Technician
                </MenuItem>
              </Menu>
            </Box>
          </Toolbar>
        </AppBar>

        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
        >
          <Box sx={{ width: 280, pt: 8 }}>
            <Typography variant="h6" sx={{ px: 2, pb: 2, textAlign: 'center' }}>
              Navigation
            </Typography>
            <List>
              {filteredMenuItems.map((item) => (
                <ListItem
                  button
                  key={item.id}
                  onClick={() => {
                    setCurrentView(item.id)
                    setDrawerOpen(false)
                  }}
                  selected={currentView === item.id}
                >
                  <ListItemIcon>
                    {item.id === 'alerts' ? (
                      <Badge badgeContent={openAlerts} color="error">
                        {item.icon}
                      </Badge>
                    ) : (
                      item.icon
                    )}
                  </ListItemIcon>
                  <ListItemText primary={item.label} />
                </ListItem>
              ))}
            </List>
          </Box>
        </Drawer>

        <Container maxWidth="xl" sx={{ mt: 10 }}>
          {renderCurrentView()}
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default App
