import React, { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Switch,
  FormControlLabel,
  Menu,
  MenuItem,
  Badge,
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard,
  Devices,
  Build,
  Assignment,
  Description,
  Warning,
  Brightness4,
  Brightness7,
  AccountCircle,
  QrCodeScanner,
} from '@mui/icons-material'
import { ThemeProvider, createTheme } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'

import DeviceInventoryDashboard from './components/DeviceInventoryDashboard'
import InstallationTrainingModule from './components/InstallationTrainingModule'
import ServiceVisitLogs from './components/ServiceVisitLogs'
import AmcCmcTracker from './components/AmcCmcTracker'
import AlertsPhotoLogs from './components/AlertsPhotoLogs'
import QRCodeScanner from './components/QRCodeScanner'

import { toggleTheme, setThemeMode } from './store/slices/themeSlice'
import { switchRole } from './store/slices/authSlice'

function App() {
  const dispatch = useDispatch()
  const { mode, primaryColor, secondaryColor } = useSelector(state => state.theme)
  const { user } = useSelector(state => state.auth)
  const { alerts } = useSelector(state => state.alerts)
  
  const [drawerOpen, setDrawerOpen] = useState(false)
  const [currentView, setCurrentView] = useState('dashboard')
  const [anchorEl, setAnchorEl] = useState(null)

  const theme = createTheme({
    palette: {
      mode,
      primary: { main: primaryColor },
      secondary: { main: secondaryColor },
    },
  })

  const openAlerts = alerts.filter(alert => alert.status === 'Open').length

  const menuItems = [
    { id: 'dashboard', label: 'Device Inventory', icon: <Dashboard />, roles: ['Admin', 'Technician'] },
    { id: 'installations', label: 'Installation & Training', icon: <Build />, roles: ['Admin', 'Technician'] },
    { id: 'service', label: 'Service Visit Logs', icon: <Assignment />, roles: ['Admin', 'Technician'] },
    { id: 'amc-cmc', label: 'AMC/CMC Tracker', icon: <Description />, roles: ['Admin'] },
    { id: 'alerts', label: 'Alerts & Photo Logs', icon: <Warning />, roles: ['Admin', 'Technician'] },
    { id: 'qr-scanner', label: 'QR Code Scanner', icon: <QrCodeScanner />, roles: ['Admin', 'Technician'] },
  ]

  const filteredMenuItems = menuItems.filter(item => 
    item.roles.includes(user?.role)
  )

  const handleThemeToggle = () => {
    dispatch(toggleTheme())
  }

  const handleRoleSwitch = (role) => {
    dispatch(switchRole(role))
    setAnchorEl(null)
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return <DeviceInventoryDashboard />
      case 'installations':
        return <InstallationTrainingModule />
      case 'service':
        return <ServiceVisitLogs />
      case 'amc-cmc':
        return <AmcCmcTracker />
      case 'alerts':
        return <AlertsPhotoLogs />
      case 'qr-scanner':
        return <QRCodeScanner />
      default:
        return <DeviceInventoryDashboard />
    }
  }

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box className="app">
        <AppBar
          position="fixed"
          sx={{
            background: 'linear-gradient(135deg, var(--primary-color), var(--primary-light))',
            backdropFilter: 'blur(10px)',
            borderBottom: '1px solid rgba(255,255,255,0.1)',
            boxShadow: 'var(--shadow-lg)',
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setDrawerOpen(true)}
                sx={{
                  mr: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'scale(1.1)',
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  }
                }}
              >
                <MenuIcon />
            </IconButton>
              <Typography
                variant="h6"
                component="div"
                sx={{
                  flexGrow: 1,
                  fontWeight: 700,
                  background: 'linear-gradient(45deg, #ffffff, #f0f9ff)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                }}
              >
                Device CRM + Inventory Management
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Badge
                badgeContent={openAlerts}
                color="error"
                sx={{
                  '& .MuiBadge-badge': {
                    animation: openAlerts > 0 ? 'pulse 2s infinite' : 'none',
                  }
                }}
              >
                <IconButton
                  color="inherit"
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                    }
                  }}
                >
                  <Warning />
                </IconButton>
              </Badge>

              <FormControlLabel
                control={
                  <Switch
                    checked={mode === 'dark'}
                    onChange={handleThemeToggle}
                    icon={<Brightness7 />}
                    checkedIcon={<Brightness4 />}
                    sx={{
                      '& .MuiSwitch-switchBase': {
                        transition: 'all 0.3s ease',
                      },
                      '& .MuiSwitch-thumb': {
                        boxShadow: 'var(--shadow-md)',
                      }
                    }}
                  />
                }
                label=""
              />

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.8)' }}>
                  {user?.role}
                </Typography>
                <IconButton
                  color="inherit"
                  onClick={(e) => setAnchorEl(e.currentTarget)}
                  sx={{
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.1)',
                      backgroundColor: 'rgba(255,255,255,0.1)',
                    }
                  }}
                >
                  <AccountCircle />
                </IconButton>
              </Box>

              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={() => setAnchorEl(null)}
                PaperProps={{
                  sx: {
                    background: 'var(--background-primary)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid var(--border-color)',
                    borderRadius: 'var(--radius-lg)',
                    boxShadow: 'var(--shadow-xl)',
                    mt: 1,
                  }
                }}
              >
                <MenuItem
                  onClick={() => handleRoleSwitch('Admin')}
                  sx={{
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      background: 'var(--background-secondary)',
                      transform: 'translateX(4px)',
                    }
                  }}
                >
                  Switch to Admin
                </MenuItem>
                <MenuItem
                  onClick={() => handleRoleSwitch('Technician')}
                  sx={{
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      background: 'var(--background-secondary)',
                      transform: 'translateX(4px)',
                    }
                  }}
                >
                  Switch to Technician
                </MenuItem>
              </Menu>
            </Box>
          </Toolbar>
        </AppBar>

        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={() => setDrawerOpen(false)}
          PaperProps={{
            sx: {
              background: 'linear-gradient(180deg, var(--background-primary), var(--background-secondary))',
              backdropFilter: 'blur(10px)',
              border: 'none',
              borderRight: '1px solid var(--border-color)',
              boxShadow: 'var(--shadow-xl)',
            }
          }}
        >
          <Box sx={{ width: 280, pt: 8 }}>
            <Box sx={{
              px: 2,
              pb: 2,
              borderBottom: '1px solid var(--border-color)',
              mb: 1
            }}>
              <Typography
                variant="h6"
                sx={{
                  color: 'var(--text-primary)',
                  fontWeight: 600,
                  textAlign: 'center'
                }}
              >
                Navigation
              </Typography>
            </Box>
            <List sx={{ px: 1 }}>
              {filteredMenuItems.map((item, index) => (
                <ListItem
                  button
                  key={item.id}
                  onClick={() => {
                    setCurrentView(item.id)
                    setDrawerOpen(false)
                  }}
                  selected={currentView === item.id}
                  sx={{
                    borderRadius: 'var(--radius-lg)',
                    mb: 0.5,
                    mx: 1,
                    transition: 'all var(--transition-normal)',
                    animation: `slideInFromRight 0.3s ease-out ${index * 0.1}s both`,
                    '&:hover': {
                      background: 'var(--background-secondary)',
                      transform: 'translateX(8px)',
                      boxShadow: 'var(--shadow-md)',
                    },
                    '&.Mui-selected': {
                      background: 'linear-gradient(135deg, var(--primary-color), var(--primary-light))',
                      color: 'white',
                      '&:hover': {
                        background: 'linear-gradient(135deg, var(--primary-dark), var(--primary-color))',
                      },
                      '& .MuiListItemIcon-root': {
                        color: 'white',
                      },
                      '& .MuiListItemText-primary': {
                        fontWeight: 600,
                      }
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 40,
                    transition: 'all var(--transition-normal)',
                  }}>
                    {item.id === 'alerts' ? (
                      <Badge
                        badgeContent={openAlerts}
                        color="error"
                        sx={{
                          '& .MuiBadge-badge': {
                            animation: openAlerts > 0 ? 'pulse 2s infinite' : 'none',
                          }
                        }}
                      >
                        {item.icon}
                      </Badge>
                    ) : (
                      item.icon
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={item.label}
                    primaryTypographyProps={{
                      fontSize: '0.9rem',
                      fontWeight: currentView === item.id ? 600 : 400,
                    }}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        </Drawer>

        <Container
          maxWidth="xl"
          className="main-content fade-in"
          sx={{
            mt: 10,
            background: 'var(--background-secondary)',
            borderRadius: 'var(--radius-xl)',
            boxShadow: 'var(--shadow-lg)',
            border: '1px solid var(--border-color)',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '2px',
              background: 'linear-gradient(90deg, var(--primary-color), var(--primary-light), var(--success-color))',
            }
          }}
        >
          <Box className="scale-in">
            {renderCurrentView()}
          </Box>
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default App
