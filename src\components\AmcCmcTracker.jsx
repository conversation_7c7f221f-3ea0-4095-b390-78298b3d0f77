import React, { useState } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Paper,
  IconButton,
  Alert,
  AlertTitle,
} from '@mui/material'
import {
  Add,
  Edit,
  Delete,
  Warning,
  GetApp,
  Refresh,
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import {
  addContract,
  updateContract,
  deleteContract,
  renewContract,
} from '../store/slices/amcCmcSlice'

const AmcCmcTracker = () => {
  const dispatch = useDispatch()
  const { contracts } = useSelector(state => state.amcCmc)
  const { devices } = useSelector(state => state.devices)
  const { user } = useSelector(state => state.auth)
  
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingContract, setEditingContract] = useState(null)
  const [formData, setFormData] = useState({
    deviceId: '',
    deviceType: '',
    facility: '',
    contractType: 'AMC',
    startDate: '',
    endDate: '',
    vendor: '',
    contractValue: '',
    currency: 'USD',
    serviceFrequency: 'Quarterly',
    contactPerson: '',
    contactEmail: '',
    contactPhone: '',
    terms: '',
    renewalNotificationDays: 30,
  })

  const handleAddContract = () => {
    setEditingContract(null)
    setFormData({
      deviceId: '',
      deviceType: '',
      facility: '',
      contractType: 'AMC',
      startDate: '',
      endDate: '',
      vendor: '',
      contractValue: '',
      currency: 'USD',
      serviceFrequency: 'Quarterly',
      contactPerson: '',
      contactEmail: '',
      contactPhone: '',
      terms: '',
      renewalNotificationDays: 30,
    })
    setDialogOpen(true)
  }

  const handleEditContract = (contract) => {
    setEditingContract(contract)
    setFormData(contract)
    setDialogOpen(true)
  }

  const handleSaveContract = () => {
    const contractData = {
      ...formData,
      status: 'Active',
      lastServiceDate: null,
      nextServiceDate: null,
    }

    if (editingContract) {
      dispatch(updateContract({ ...contractData, id: editingContract.id }))
    } else {
      dispatch(addContract(contractData))
    }
    setDialogOpen(false)
  }

  const handleDeleteContract = (contractId) => {
    if (window.confirm('Are you sure you want to delete this contract?')) {
      dispatch(deleteContract(contractId))
    }
  }

  const handleRenewContract = (contractId) => {
    const contract = contracts.find(c => c.id === contractId)
    if (contract) {
      const currentEndDate = new Date(contract.endDate)
      const newEndDate = new Date(currentEndDate.getFullYear() + 1, currentEndDate.getMonth(), currentEndDate.getDate())
      dispatch(renewContract({
        contractId,
        newEndDate: newEndDate.toISOString().split('T')[0]
      }))
    }
  }

  const getExpiringContracts = () => {
    const today = new Date()
    const thirtyDaysFromNow = new Date(today.getTime() + (30 * 24 * 60 * 60 * 1000))
    
    return contracts.filter(contract => {
      const endDate = new Date(contract.endDate)
      return endDate <= thirtyDaysFromNow && contract.status === 'Active'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'Active': return 'success'
      case 'Expired': return 'error'
      case 'Expiring Soon': return 'warning'
      default: return 'default'
    }
  }

  const getContractStatus = (contract) => {
    const today = new Date()
    const endDate = new Date(contract.endDate)
    const daysUntilExpiry = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24))
    
    if (daysUntilExpiry < 0) return 'Expired'
    if (daysUntilExpiry <= contract.renewalNotificationDays) return 'Expiring Soon'
    return 'Active'
  }

  const exportToCSV = () => {
    const headers = ['Contract ID', 'Device ID', 'Device Type', 'Facility', 'Contract Type', 'Start Date', 'End Date', 'Status', 'Vendor', 'Value', 'Currency']
    const csvContent = [
      headers.join(','),
      ...contracts.map(contract => [
        contract.id,
        contract.deviceId,
        contract.deviceType,
        contract.facility,
        contract.contractType,
        contract.startDate,
        contract.endDate,
        getContractStatus(contract),
        contract.vendor,
        contract.contractValue,
        contract.currency
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'amc_cmc_contracts.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const columns = [
    { field: 'id', headerName: 'Contract ID', width: 120 },
    { field: 'deviceId', headerName: 'Device ID', width: 120 },
    { field: 'deviceType', headerName: 'Device Type', width: 150 },
    { field: 'facility', headerName: 'Facility', width: 150 },
    { field: 'contractType', headerName: 'Type', width: 80 },
    { field: 'startDate', headerName: 'Start Date', width: 120 },
    { field: 'endDate', headerName: 'End Date', width: 120 },
    {
      field: 'status',
      headerName: 'Status',
      width: 130,
      renderCell: (params) => {
        const status = getContractStatus(params.row)
        return (
          <Chip
            label={status}
            color={getStatusColor(status)}
            size="small"
          />
        )
      },
    },
    { field: 'vendor', headerName: 'Vendor', width: 150 },
    {
      field: 'contractValue',
      headerName: 'Value',
      width: 120,
      renderCell: (params) => (
        <Typography variant="body2">
          {params.row.currency} {params.value?.toLocaleString()}
        </Typography>
      ),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 180,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => handleEditContract(params.row)}
            disabled={!user?.permissions.includes('write')}
          >
            <Edit />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleRenewContract(params.row.id)}
            disabled={!user?.permissions.includes('write')}
          >
            <Refresh />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDeleteContract(params.row.id)}
            disabled={!user?.permissions.includes('delete')}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ]

  const expiringContracts = getExpiringContracts()

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          AMC/CMC Tracker
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<GetApp />}
            onClick={exportToCSV}
            disabled={!user?.permissions.includes('export')}
          >
            Export CSV
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddContract}
            disabled={!user?.permissions.includes('write')}
          >
            Add Contract
          </Button>
        </Box>
      </Box>

      {expiringContracts.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <AlertTitle>Contracts Expiring Soon</AlertTitle>
          {expiringContracts.length} contract(s) are expiring within 30 days. Please review and renew as needed.
        </Alert>
      )}

      <Paper>
        <DataGrid
          rows={contracts}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 10 },
            },
          }}
          pageSizeOptions={[10, 25, 50]}
          autoHeight
          disableRowSelectionOnClick
        />
      </Paper>

      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingContract ? 'Edit Contract' : 'Add New Contract'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Device</InputLabel>
                <Select
                  value={formData.deviceId}
                  onChange={(e) => {
                    const device = devices.find(d => d.id === e.target.value)
                    setFormData({
                      ...formData,
                      deviceId: e.target.value,
                      deviceType: device?.type || '',
                      facility: device?.facility || '',
                    })
                  }}
                >
                  {devices.map((device) => (
                    <MenuItem key={device.id} value={device.id}>
                      {device.id} - {device.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Contract Type</InputLabel>
                <Select
                  value={formData.contractType}
                  onChange={(e) => setFormData({ ...formData, contractType: e.target.value })}
                >
                  <MenuItem value="AMC">AMC (Annual Maintenance Contract)</MenuItem>
                  <MenuItem value="CMC">CMC (Comprehensive Maintenance Contract)</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Vendor"
                value={formData.vendor}
                onChange={(e) => setFormData({ ...formData, vendor: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Contract Value"
                type="number"
                value={formData.contractValue}
                onChange={(e) => setFormData({ ...formData, contractValue: parseFloat(e.target.value) })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSaveContract} variant="contained">
            {editingContract ? 'Update' : 'Add'} Contract
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default AmcCmcTracker
